# Render Environment Variables Template
# Copy this file to your Render dashboard environment variables section

# Application
NODE_ENV=production
PORT=3000

# Next.js
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=https://your-app-name.onrender.com

# Database (Automatically provided by Render PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database

# Redis (Automatically provided by Render Redis)
REDIS_URL=redis://username:password@host:port

# Email Configuration (Choose one)
# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM="Your App Name <<EMAIL>>"

# OR Mailgun Configuration
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain

# SMS/Phone Verification (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# WebAuthn Configuration
WEBAUTHN_RP_ID=your-app-name.onrender.com
WEBAUTHN_ORIGIN=https://your-app-name.onrender.com

# Logging
LOG_PATH=/tmp/logs
LOG_MAX_FILES=5
LOG_MAX_SIZE=100m

# Backup
BACKUP_STORAGE_PATH=/tmp/backups
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# Development (set to false in production)
NEXT_PUBLIC_BYPASS_AUTH=false

# Optional: Analytics and Monitoring
# SENTRY_DSN=your-sentry-dsn
# GOOGLE_ANALYTICS_ID=your-ga-id
