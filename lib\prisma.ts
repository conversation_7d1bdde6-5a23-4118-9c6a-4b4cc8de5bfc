import { PrismaClient } from '@prisma/client'
import mockPrisma from './mock-prisma'

// Check if we're in development mode
const isDev = process.env.NODE_ENV === 'development'

// Function to test database connection
const testDatabaseConnection = async (prisma: PrismaClient): Promise<boolean> => {
  try {
    // Try a simple query
    await prisma.$queryRaw`SELECT 1 as test`
    console.log('Database connection successful')
    return true
  } catch (error) {
    console.warn('Database connection failed:', error)
    return false
  }
}

// Initialize Prisma client
const initPrismaClient = async (): Promise<PrismaClient> => {
  // Create a new Prisma client
  const client = new PrismaClient({
    log: ['query'],
  })

  // Test the connection
  const isConnected = await testDatabaseConnection(client)

  if (isConnected) {
    return client
  } else {
    console.warn('Using mock Prisma client due to database connection failure')
    return mockPrisma as unknown as PrismaClient
  }
}

// Global variable to store the Prisma client
const globalForPrisma = global as unknown as {
  prisma: PrismaClient | undefined
  prismaPromise: Promise<PrismaClient> | undefined
}

// Initialize the Prisma client if it doesn't exist
if (!globalForPrisma.prismaPromise) {
  globalForPrisma.prismaPromise = initPrismaClient()
    .then(client => {
      globalForPrisma.prisma = client
      return client
    })
    .catch(error => {
      console.error('Failed to initialize Prisma client:', error)
      // Fall back to mock client
      const mockClient = mockPrisma as unknown as PrismaClient
      globalForPrisma.prisma = mockClient
      return mockClient
    })
}

// Export the Prisma client
export const prismaPromise = globalForPrisma.prismaPromise
export const prisma = globalForPrisma.prisma || mockPrisma as unknown as PrismaClient

export default prisma
