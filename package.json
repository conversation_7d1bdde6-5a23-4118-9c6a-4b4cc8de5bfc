{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@prisma/client": "^5.22.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@react-navigation/native": "^7.0.3", "@react-navigation/native-stack": "^7.1.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "face-api.js": "^0.22.2", "form-data": "^4.0.2", "framer-motion": "^11.13.1", "ioredis": "^5.5.0", "jose": "^5.10.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "mailgun.js": "^12.0.1", "module-name": "^0.0.1-security", "next": "^14.2.16", "next-auth": "^4.24.10", "next-themes": "^0.4.4", "nodemailer": "^6.10.0", "oauth-1.0a": "^2.2.6", "react": "^18", "react-dom": "^18", "react-icons": "^5.3.0", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.2.0", "react-router-dom": "^6.28.0", "recharts": "^2.15.1", "redis": "^4.7.0", "sonner": "^1.0.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "prisma": "^5.22.0", "tailwindcss": "^3.4.15", "typescript": "^5"}}