generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String             @id @default(cuid())
  email             String             @unique
  password          String
  name              String?
  emailVerified     Boolean            @default(false)
  phoneNumber       String?            @unique
  dateOfBirth       DateTime?
  address           String?
  profileImage      String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  verificationCodes VerificationCode[]
  accounts          Account[]
  cards             Card[]
  transactions      Transaction[]
  beneficiaries     Beneficiary[]
  investments       Investment[]
  savings           SavingsGoal[]
  kycDocuments      KycDocument[]
  notifications     Notification[]
  AuditLog          AuditLog[]
  Session           Session[]
  TransactionLog    TransactionLog[]
}

model VerificationCode {
  id        String   @id @default(cuid())
  code      String
  type      String   @default("EMAIL_VERIFICATION")
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  expiresAt DateTime
  createdAt DateTime @default(now())
  used      Boolean  @default(false)
}

model Account {
  id           String        @id @default(cuid())
  userId       String
  user         User          @relation(fields: [userId], references: [id])
  type         AccountType
  number       String        @unique
  balance      Float         @default(0)
  currency     String        @default("USD")
  isActive     Boolean       @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  transactions Transaction[]
  cards        Card[]
}

enum AccountType {
  SAVINGS
  CHECKING
  INVESTMENT
}

model Card {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id])
  accountId   String
  account     Account  @relation(fields: [accountId], references: [id])
  type        CardType
  number      String   @unique
  expiryMonth Int
  expiryYear  Int
  cvv         String
  pin         String
  isActive    Boolean  @default(true)
  dailyLimit  Float    @default(1000)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum CardType {
  VIRTUAL
  PHYSICAL
  DEBIT
  CREDIT
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  PAYMENT
  REFUND
  FEE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

model Transaction {
  id            String            @id @default(cuid())
  userId        String
  user          User              @relation(fields: [userId], references: [id])
  type          TransactionType
  amount        Float
  description   String?
  category      String?
  merchant      String?
  status        TransactionStatus @default(PENDING)
  beneficiaryId String?
  beneficiary   Beneficiary?      @relation(fields: [beneficiaryId], references: [id])
  investmentId  String?
  investment    Investment?       @relation(fields: [investmentId], references: [id])
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  Account       Account?          @relation(fields: [accountId], references: [id])
  accountId     String?
}

model Beneficiary {
  id            String        @id @default(cuid())
  userId        String
  user          User          @relation(fields: [userId], references: [id])
  name          String
  accountNumber String
  bankName      String
  bankCode      String?
  email         String?
  phoneNumber   String?
  isActive      Boolean       @default(true)
  transactions  Transaction[]
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
}

enum InvestmentType {
  STOCK
  BOND
  MUTUAL_FUND
  ETF
  REAL_ESTATE
  CRYPTO
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
}

enum InvestmentStatus {
  ACTIVE
  CLOSED
  PENDING
}

model Investment {
  id           String           @id @default(cuid())
  userId       String
  user         User             @relation(fields: [userId], references: [id])
  type         InvestmentType
  amount       Float
  return       Float?
  risk         RiskLevel
  description  String?
  status       InvestmentStatus @default(ACTIVE)
  startDate    DateTime         @default(now())
  endDate      DateTime?
  transactions Transaction[]
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  performance  Json[]
}

model SavingsGoal {
  id             String                @id @default(cuid())
  userId         String
  name           String
  targetAmount   Float
  currentAmount  Float                 @default(0)
  deadline       DateTime
  category       String
  description    String?
  isAutoSave     Boolean               @default(false)
  autoSaveAmount Float?
  riskLevel      String                @default("LOW")
  status         String                @default("ACTIVE")
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt
  user           User                  @relation(fields: [userId], references: [id])
  contributions  SavingsContribution[]
}

model SavingsContribution {
  id          String      @id @default(cuid())
  goalId      String
  amount      Float
  type        String      @default("MANUAL") // MANUAL, AUTO, BONUS
  description String?
  createdAt   DateTime    @default(now())
  goal        SavingsGoal @relation(fields: [goalId], references: [id], onDelete: Cascade)

  @@index([goalId])
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   Json
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([timestamp])
}

model SecurityAlert {
  id        String   @id @default(cuid())
  type      String
  severity  String
  message   String
  metadata  Json?
  resolved  Boolean  @default(false)
  timestamp DateTime @default(now())

  @@index([type])
  @@index([severity])
  @@index([timestamp])
}

model SystemMetric {
  id        String   @id @default(cuid())
  name      String
  value     Float
  unit      String
  timestamp DateTime @default(now())

  @@index([name])
  @@index([timestamp])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model KycDocument {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  type      String
  status    String
  data      Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  message   String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CertificateCheck {
  id              String   @id @default(cuid())
  certificateId   String // CN from the certificate
  checkDate       DateTime @default(now())
  daysUntilExpiry Int
  isValid         Boolean
  createdAt       DateTime @default(now())
}

model TransactionLog {
  id        String   @id @default(cuid())
  userId    String
  type      String
  amount    Float
  status    String
  provider  String
  error     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([createdAt])
  @@index([status])
}
