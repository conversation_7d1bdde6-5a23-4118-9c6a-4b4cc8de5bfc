services:
  # Web Service (Next.js Application)
  - type: web
    name: hoardrun-web
    env: node
    plan: starter # Change to 'standard' or 'pro' for production
    buildCommand: npm ci && npm run build
    startCommand: npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_BYPASS_AUTH
        sync: false # Set this in Render dashboard
      - key: DATABASE_URL
        fromDatabase:
          name: hoardrun-db
          property: connectionString
      - key: NEXTAUTH_SECRET
        generateValue: true
      - key: NEXTAUTH_URL
        value: https://hoardrun-web.onrender.com # Update with your actual domain
      - key: SMTP_HOST
        sync: false
      - key: SMTP_PORT
        sync: false
      - key: SMTP_USER
        sync: false
      - key: SMTP_PASS
        sync: false
      - key: SMTP_FROM
        sync: false
      - key: MAILGUN_API_KEY
        sync: false
      - key: MAILGUN_DOMAIN
        sync: false
      - key: TWILIO_ACCOUNT_SID
        sync: false
      - key: TWILIO_AUTH_TOKEN
        sync: false
      - key: TWILIO_PHONE_NUMBER
        sync: false
      - key: REDIS_URL
        fromService:
          type: redis
          name: hoardrun-redis
          property: connectionString
      - key: WEBAUTHN_RP_ID
        value: hoardrun-web.onrender.com # Update with your actual domain
      - key: WEBAUTHN_ORIGIN
        value: https://hoardrun-web.onrender.com # Update with your actual domain
      - key: LOG_PATH
        value: /tmp/logs
      - key: LOG_MAX_FILES
        value: "5"
      - key: LOG_MAX_SIZE
        value: "100m"
      - key: BACKUP_STORAGE_PATH
        value: /tmp/backups
      - key: BACKUP_RETENTION_DAYS
        value: "30"
      - key: BACKUP_ENCRYPTION_KEY
        generateValue: true

  # Background Worker (Optional - for processing jobs)
  - type: worker
    name: hoardrun-worker
    env: node
    buildCommand: npm ci
    startCommand: node scripts/worker.js
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: hoardrun-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: hoardrun-redis
          property: connectionString

databases:
  # PostgreSQL Database
  - name: hoardrun-db
    databaseName: hoardrun
    user: hoardrun_user
    plan: starter # Change to 'standard' or 'pro' for production

  # Redis Cache
  - name: hoardrun-redis
    plan: starter # Change to 'standard' or 'pro' for production

# Static Site (Optional - for documentation or landing page)
# - type: static
#   name: hoardrun-docs
#   buildCommand: npm run build:docs
#   staticPublishPath: ./docs/dist
#   routes:
#     - type: rewrite
#       source: /*
#       destination: /index.html
