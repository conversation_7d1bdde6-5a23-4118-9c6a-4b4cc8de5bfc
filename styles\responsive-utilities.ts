export const responsiveStyles = {
  // Container widths
  container: 'w-full max-w-[95%] sm:max-w-[540px] md:max-w-[720px] lg:max-w-[960px] xl:max-w-[1140px] mx-auto',
  
  // Typography
  heading1: 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold',
  heading2: 'text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold',
  heading3: 'text-lg sm:text-xl md:text-2xl lg:text-3xl font-medium',
  bodyText: 'text-sm sm:text-base md:text-lg',
  
  // Spacing
  section: 'py-6 sm:py-8 md:py-12 lg:py-16',
  sectionInner: 'space-y-4 sm:space-y-6 md:space-y-8',
  
  // Card layouts
  cardGrid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8',
  card: 'p-4 sm:p-6 md:p-8',
  
  // Form elements
  formGroup: 'space-y-2 sm:space-y-3',
  input: 'w-full px-3 py-2 sm:py-3',
  button: 'px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base',
  
  // Navigation
  nav: 'flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 md:space-x-6',
  
  // Flexbox utilities
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  
  // Responsive padding and margin
  padding: 'p-4 sm:p-6 md:p-8 lg:p-10',
  margin: 'm-4 sm:m-6 md:m-8 lg:m-10',
}; 